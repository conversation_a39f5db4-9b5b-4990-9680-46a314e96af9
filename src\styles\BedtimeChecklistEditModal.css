/* 睡前檢查表編輯模態視窗 - 參考TaskModal設計 */
.bedtime-checklist-edit-modal .modal-container {
  max-height: 85vh;
  overflow: hidden;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  padding: 0; /* 移除預設padding，讓內部元素控制間距 */
  display: flex;
  flex-direction: column;
}

/* 模態視窗標題區域 - 參考TaskModal */
.bedtime-checklist-edit-modal .modal-header {
  padding: 24px 24px 0 24px;
  flex-shrink: 0;
  color: #1f2937;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.bedtime-checklist-edit-modal .modal-header::before {
  content: "🌙";
  font-size: 24px;
}

.bedtime-checklist-edit-modal .modal-description {
  color: #4b5563;
  font-size: var(--font-size-md) !important;
  font-weight: 500;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* 模態視窗內容區域 - 參考TaskModal */
.bedtime-edit-modal-content {
  padding: 0 24px;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

/* 載入狀態 */
.bedtime-edit-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.bedtime-edit-loading-spinner {
  margin-bottom: 16px;
}

.bedtime-checklist-edit-modal .bedtime-edit-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: bedtime-edit-spin 1s linear infinite;
}

@keyframes bedtime-edit-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.bedtime-checklist-edit-modal .loading-container p {
  color: #6b7280;
  font-size: var(--font-size-xs);
  margin: 0;
}

/* 工具列 */
.bedtime-edit-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止被壓縮 */
}

.bedtime-edit-toolbar-left .stats {
  display: flex;
  gap: 16px;
}

.bedtime-edit-stat-item {
  font-size: var(--font-size-sm);
  color: #6b7280;
  background: #f8fafc;
  padding: 8px 14px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.bedtime-edit-toolbar-right {
  display: flex;
  gap: 12px;
}

.bedtime-edit-reset-default-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bedtime-edit-reset-default-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.bedtime-edit-add-category-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bedtime-edit-add-category-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* 編輯區域 */
.bedtime-edit-area {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

.bedtime-edit-area::-webkit-scrollbar {
  width: 6px;
}

.bedtime-edit-area::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.bedtime-edit-area::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.bedtime-edit-area::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 空狀態 */
.bedtime-edit-empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  background: #f8fafc;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  margin: 20px 0;
}

.bedtime-edit-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.bedtime-edit-empty-placeholder h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.bedtime-edit-empty-placeholder p {
  font-size: var(--font-size-xs);
  margin: 0 0 20px 0;
  color: #6b7280;
  line-height: 1.5;
}

.bedtime-edit-empty-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bedtime-edit-empty-action-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* 類別列表 */
.bedtime-edit-categories-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 類別卡片 */
.bedtime-edit-category-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.bedtime-edit-category-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d1d5db;
}

/* 類別標題區 */
.bedtime-edit-category-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  gap: 12px;
}

.bedtime-edit-category-badge {
  width: 28px;
  height: 28px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: 600;
  flex-shrink: 0;
}

.bedtime-edit-category-inputs {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.bedtime-edit-emoji-input {
  width: 50px;
  height: 40px;
  text-align: center;
  font-size: 18px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  transition: all 0.2s ease;
}

.bedtime-edit-emoji-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.bedtime-edit-category-title-input {
  flex: 1;
  height: 40px;
  padding: 0 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  background: white;
  transition: all 0.2s ease;
}

.bedtime-edit-category-title-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.bedtime-edit-category-actions {
  display: flex;
  gap: 6px;
}

.bedtime-edit-btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: var(--font-size-xs);
}

.bedtime-edit-btn-add {
  background: #10b981;
  color: white;
}

.bedtime-edit-btn-add:hover {
  background: #059669;
  transform: scale(1.05);
}

.bedtime-edit-btn-delete {
  background: #ef4444;
  color: white;
}

.bedtime-edit-btn-delete:hover {
  background: #dc2626;
  transform: scale(1.05);
}

/* 子項目容器 */
.bedtime-edit-subitems-container {
  padding: 16px;
  background: #ffffff;
}

.bedtime-checklist-edit-modal .subitems-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: #374151;
}

.bedtime-checklist-edit-modal .count {
  color: #6b7280;
  font-weight: 400;
}

.bedtime-checklist-edit-modal .subitems-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.bedtime-checklist-edit-modal .subitem-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.bedtime-checklist-edit-modal .subitem-row:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.bedtime-checklist-edit-modal .subitem-number {
  width: 22px;
  height: 22px;
  background: #6b7280;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: 600;
  flex-shrink: 0;
}

.bedtime-checklist-edit-modal .subitem-input {
  flex: 1;
  height: 34px;
  padding: 0 10px;
  border: 1px solid #d1d5db;
  border-radius: 5px;
  font-size: var(--font-size-xs);
  background: white;
  transition: all 0.2s ease;
}

.bedtime-checklist-edit-modal .subitem-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.bedtime-checklist-edit-modal .btn-remove {
  width: 26px;
  height: 26px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: var(--font-size-xs);
  flex-shrink: 0;
}

.bedtime-checklist-edit-modal .btn-remove:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.bedtime-checklist-edit-modal .empty-subitems {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  text-align: center;
  color: #9ca3af;
  background: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
}

.bedtime-checklist-edit-modal .add-first-subitem {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.bedtime-checklist-edit-modal .add-first-subitem:hover {
  background: #2563eb;
}

/* 底部操作區 - 參考TaskModal固定設計 */
.bedtime-edit-modal-actions {
  padding: 16px 24px 24px 24px;
  border-top: 1px solid #e5e5e5;
  background: white;
  flex-shrink: 0;
  margin-top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bedtime-edit-actions-left {
  flex: 1;
}

.bedtime-edit-help-text {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px !important;
  color: #4b5563;
  font-weight: 600 !important;
  line-height: 1.4;
}

.bedtime-edit-help-text svg {
  font-size: 18px !important;
  flex-shrink: 0;
}

.bedtime-edit-actions-right {
  display: flex;
  gap: 12px;
}

.bedtime-edit-btn-secondary,
.bedtime-edit-btn-primary {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 100px;
  justify-content: center;
}

.bedtime-edit-btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.bedtime-edit-btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.bedtime-edit-btn-primary {
  background: #10b981;
  color: white;
}

.bedtime-edit-btn-primary:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.bedtime-edit-btn-primary:disabled,
.bedtime-edit-btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .bedtime-checklist-edit-modal .modal-container {
    width: 95%;
    max-height: 90vh;
    margin: 20px;
  }

  .bedtime-checklist-edit-modal .modal-header {
    font-size: 20px;
    padding: 16px 16px 0 16px;
  }

  .bedtime-checklist-edit-modal .edit-modal-content {
    padding: 0 16px;
  }

  .bedtime-checklist-edit-modal .modal-actions {
    padding: 12px 16px 16px 16px;
  }

  .bedtime-checklist-edit-modal .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .bedtime-checklist-edit-modal .toolbar-left .stats {
    justify-content: center;
  }

  .bedtime-checklist-edit-modal .toolbar-right {
    flex-direction: column;
    gap: 8px;
  }

  .bedtime-checklist-edit-modal .reset-default-btn,
  .bedtime-checklist-edit-modal .add-category-btn {
    width: 100%;
    justify-content: center;
  }

  .bedtime-checklist-edit-modal .category-inputs {
    flex-direction: column;
    gap: 8px;
  }

  .bedtime-checklist-edit-modal .emoji-input {
    width: 60px;
    align-self: center;
  }

  .bedtime-checklist-edit-modal .category-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .bedtime-checklist-edit-modal .category-actions {
    justify-content: center;
  }

  .bedtime-checklist-edit-modal .modal-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .bedtime-checklist-edit-modal .actions-right {
    justify-content: center;
  }

  .bedtime-checklist-edit-modal .btn-secondary,
  .bedtime-checklist-edit-modal .btn-primary {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .bedtime-checklist-edit-modal .modal-container {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
  }

  .bedtime-checklist-edit-modal .modal-header {
    font-size: 18px;
    padding: 12px 12px 0 12px;
  }

  .bedtime-checklist-edit-modal .edit-modal-content {
    padding: 0 12px;
  }

  .bedtime-checklist-edit-modal .modal-actions {
    padding: 12px 12px 12px 12px;
  }

  .bedtime-checklist-edit-modal .category-card {
    border-radius: 8px;
  }

  .bedtime-checklist-edit-modal .category-header {
    padding: 12px;
  }

  .bedtime-checklist-edit-modal .category-badge {
    width: 24px;
    height: 24px;
    font-size: var(--font-size-xs);
  }

  .bedtime-checklist-edit-modal .emoji-input {
    width: 44px;
    height: 36px;
    font-size: 16px;
  }

  .bedtime-checklist-edit-modal .category-title-input {
    height: 36px;
    font-size: var(--font-size-xs);
  }

  .bedtime-checklist-edit-modal .btn-icon {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-xs);
  }

  .bedtime-checklist-edit-modal .subitems-container {
    padding: 12px;
  }

  .bedtime-checklist-edit-modal .subitem-row {
    padding: 8px;
    gap: 8px;
  }

  .bedtime-checklist-edit-modal .subitem-number {
    width: 18px;
    height: 18px;
    font-size: var(--font-size-xs);
  }

  .bedtime-checklist-edit-modal .subitem-input {
    height: 30px;
    font-size: var(--font-size-xs);
  }

  .bedtime-checklist-edit-modal .btn-remove {
    width: 22px;
    height: 22px;
    font-size: var(--font-size-xs);
  }

  .bedtime-checklist-edit-modal .actions-right {
    flex-direction: column;
    gap: 8px;
  }

  .bedtime-checklist-edit-modal .btn-secondary,
  .bedtime-checklist-edit-modal .btn-primary {
    width: 100%;
    padding: 12px;
  }

  .bedtime-checklist-edit-modal .modal-actions .help-text {
    text-align: center;
    font-size: 18px !important;
    font-weight: 600 !important;
    line-height: 1.4;
  }

  .bedtime-checklist-edit-modal .modal-actions .help-text svg {
    font-size: 18px !important;
  }
}

/* 剩餘的重要類別樣式 */
.bedtime-edit-subitems-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: #374151;
}

.bedtime-edit-count {
  color: #6b7280;
  font-weight: 400;
}

.bedtime-edit-subitems-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.bedtime-edit-subitem-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.bedtime-edit-subitem-row:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.bedtime-edit-subitem-number {
  width: 22px;
  height: 22px;
  background: #6b7280;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: 600;
  flex-shrink: 0;
}

.bedtime-edit-subitem-input {
  flex: 1;
  height: 34px;
  padding: 0 10px;
  border: 1px solid #d1d5db;
  border-radius: 5px;
  font-size: var(--font-size-sm);
  background: #ffffff;
  transition: all 0.2s ease;
}

.bedtime-edit-subitem-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.bedtime-edit-btn-remove {
  width: 26px;
  height: 26px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.bedtime-edit-btn-remove:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.bedtime-edit-empty-subitems {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  text-align: center;
  color: #6b7280;
  background: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
}

.bedtime-edit-add-first-subitem {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.bedtime-edit-add-first-subitem:hover {
  background: #2563eb;
}

/* ========================================
   直向模式完全重新設計
   ======================================== */

@media screen and (orientation: portrait) {
  /* 模態視窗容器 - 全螢幕設計 */
  .bedtime-checklist-edit-modal .modal-container {
    width: 100vw !important;
    height: 100vh !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }

  /* 標題區域 - 緊湊設計，減少垂直空間佔用 */
  .bedtime-checklist-edit-modal .modal-header {
    flex-shrink: 0;
    padding: 12px 16px 8px 16px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    text-align: center !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    border-bottom: none !important;
    min-height: 44px !important;
  }

  .bedtime-checklist-edit-modal .modal-header::before {
    content: "🌙" !important;
    font-size: 28px !important;
    display: block !important;
    margin-bottom: 8px !important;
  }

  .bedtime-checklist-edit-modal .modal-description {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    margin-bottom: 0 !important;
    line-height: 1.4 !important;
  }

  /* 主要內容區域 - 可滾動 */
  .bedtime-edit-modal-content {
    flex: 1 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    padding: 0 !important;
    background: #f8fafc !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* 工具列重新設計 - 緊湊卡片式，減少垂直空間 */
  .bedtime-edit-toolbar {
    flex-shrink: 0 !important;
    background: white !important;
    margin: 8px 12px 0 12px !important;
    padding: 12px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    border: none !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
  }

  /* 統計資訊 - 水平布局，節省垂直空間 */
  .bedtime-edit-toolbar-left .stats {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-around !important;
    gap: 8px !important;
    margin-bottom: 0 !important;
  }

  .bedtime-edit-stat-item {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
    border: 1px solid #e1bee7 !important;
    border-radius: 8px !important;
    padding: 8px 10px !important;
    text-align: center !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    color: #4a148c !important;
    box-shadow: 0 1px 4px rgba(156, 39, 176, 0.1) !important;
    flex: 1 !important;
    min-width: 0 !important;
  }

  .bedtime-edit-stat-item strong {
    display: block !important;
    font-size: 16px !important;
    font-weight: 800 !important;
    color: #6a1b9a !important;
    margin-bottom: 2px !important;
  }

  /* 操作按鈕 - 水平排列，節省垂直空間 */
  .bedtime-edit-toolbar-right {
    display: flex !important;
    flex-direction: row !important;
    gap: 8px !important;
  }

  .bedtime-edit-reset-default-btn,
  .bedtime-edit-add-category-btn {
    flex: 1 !important;
    height: 40px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    min-width: 0 !important;
  }

  .bedtime-edit-reset-default-btn {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
    color: white !important;
  }

  .bedtime-edit-add-category-btn {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
    color: white !important;
  }

  .bedtime-edit-reset-default-btn:active,
  .bedtime-edit-add-category-btn:active {
    transform: scale(0.96) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }

  /* 編輯區域 */
  .bedtime-edit-area {
    flex: 1 !important;
    padding: 0 16px 20px 16px !important;
    overflow-y: visible !important;
    display: block !important;
  }

  /* 類別列表 */
  .bedtime-edit-categories-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 20px !important;
    padding: 20px 0 40px 0 !important;
  }

  /* 類別卡片 - 全新設計 */
  .bedtime-edit-category-card {
    background: white !important;
    border-radius: 20px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    border: 3px solid #e8f5e8 !important;
    overflow: visible !important;
    transition: all 0.3s ease !important;
    margin-bottom: 8px !important;
  }

  .bedtime-edit-category-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
    border-color: #4caf50 !important;
  }

  /* 類別標題區 - 重新設計 */
  .bedtime-edit-category-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%) !important;
    padding: 24px 20px !important;
    border-radius: 17px 17px 0 0 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 20px !important;
    border-bottom: 2px solid #c8e6c9 !important;
  }

  /* 類別徽章 - 居中顯示 */
  .bedtime-edit-category-badge {
    width: 48px !important;
    height: 48px !important;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
    color: white !important;
    border-radius: 50% !important;
    font-size: 20px !important;
    font-weight: 800 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    align-self: center !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
  }

  /* 輸入區域 - 垂直排列 */
  .bedtime-edit-category-inputs {
    display: flex !important;
    flex-direction: column !important;
    gap: 16px !important;
    align-items: center !important;
  }

  .bedtime-edit-emoji-input {
    width: 80px !important;
    height: 80px !important;
    font-size: 32px !important;
    text-align: center !important;
    border: 3px solid #c8e6c9 !important;
    border-radius: 20px !important;
    background: white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
  }

  .bedtime-edit-emoji-input:focus {
    border-color: #4caf50 !important;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.2) !important;
    transform: scale(1.05) !important;
  }

  .bedtime-edit-category-title-input {
    width: 100% !important;
    height: 56px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    text-align: center !important;
    padding: 0 20px !important;
    border: 3px solid #c8e6c9 !important;
    border-radius: 16px !important;
    background: white !important;
    color: #2e7d32 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
  }

  .bedtime-edit-category-title-input:focus {
    border-color: #4caf50 !important;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.2) !important;
    transform: scale(1.02) !important;
  }

  /* 類別操作按鈕 */
  .bedtime-edit-category-actions {
    display: flex !important;
    justify-content: center !important;
    gap: 16px !important;
  }

  .bedtime-edit-btn-icon {
    width: 56px !important;
    height: 56px !important;
    border-radius: 16px !important;
    border: none !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .bedtime-edit-btn-add {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
    color: white !important;
  }

  .bedtime-edit-btn-delete {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
    color: white !important;
  }

  .bedtime-edit-btn-icon:active {
    transform: scale(0.9) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  }

  /* 子項目容器 - 緊湊設計，減少垂直空間 */
  .bedtime-edit-subitems-container {
    background: #fafafa !important;
    padding: 12px 16px !important;
    border-radius: 0 0 12px 12px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 30px !important;
  }

  .bedtime-edit-subitems-header {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%) !important;
    padding: 8px 12px !important;
    border-radius: 8px !important;
    margin-bottom: 12px !important;
    text-align: center !important;
    font-size: 14px !important;
    font-weight: 700 !important;
    color: #e65100 !important;
    border: 1px solid #ffcc02 !important;
    box-shadow: 0 1px 4px rgba(255, 152, 0, 0.2) !important;
  }

  .bedtime-edit-count {
    color: #f57c00 !important;
    font-weight: 800 !important;
  }

  /* 子項目列表 - 緊湊間距 */
  .bedtime-edit-subitems-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    min-height: 20px !important;
  }

  /* 子項目行 - 緊湊設計，減少高度 */
  .bedtime-edit-subitem-row {
    background: white !important;
    border-radius: 12px !important;
    padding: 8px 12px !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    border: 1px solid #e3f2fd !important;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    min-height: 44px !important;
    box-sizing: border-box !important;
  }

  .bedtime-edit-subitem-row:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
    border-color: #2196f3 !important;
  }

  /* X按鈕樣式 - 緊湊設計 */
  .bedtime-edit-subitem-row .bedtime-edit-btn-remove {
    width: 28px !important;
    height: 28px !important;
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
    color: white !important;
    border-radius: 50% !important;
    font-size: 12px !important;
    font-weight: 800 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    box-shadow: 0 1px 4px rgba(244, 67, 54, 0.3) !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
  }

  .bedtime-edit-subitem-row .bedtime-edit-btn-remove:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4) !important;
  }

  .bedtime-edit-subitem-row .bedtime-edit-btn-remove:active {
    transform: scale(0.95) !important;
  }

  .bedtime-edit-subitem-input {
    flex: 1 !important;
    min-height: 32px !important;
    max-height: 80px !important;
    height: auto !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 12px !important;
    border: 1px solid #e1f5fe !important;
    border-radius: 8px !important;
    background: white !important;
    color: #1565c0 !important;
    transition: all 0.3s ease !important;
    box-sizing: border-box !important;
    line-height: 1.3 !important;
    outline: none !important;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    overflow-wrap: break-word !important;
    resize: none !important;
    overflow-y: auto !important;
    font-family: inherit !important;
  }

  .bedtime-edit-subitem-input:focus {
    border-color: #2196f3 !important;
    background: white !important;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.15) !important;
    outline: none !important;
  }

  /* 虛擬鍵盤顯示時的特殊處理 - 直向模式 */
  .modal-overlay.keyboard-visible .bedtime-checklist-edit-modal .modal-container {
    height: 60vh !important;
    max-height: 60vh !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-modal-content {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 0 8px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-toolbar {
    margin: 4px 8px 0 8px !important;
    padding: 8px !important;
    gap: 8px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-subitems-container {
    padding: 8px 12px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-subitems-header {
    padding: 6px 10px !important;
    margin-bottom: 8px !important;
    font-size: 12px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-subitem-row {
    padding: 6px 10px !important;
    min-height: 36px !important;
    gap: 8px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-subitem-input {
    min-height: 28px !important;
    font-size: 13px !important;
    padding: 6px 10px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-btn-remove {
    width: 24px !important;
    height: 24px !important;
    font-size: 10px !important;
  }

  /* 虛擬鍵盤顯示時的底部操作區域優化 */
  .modal-overlay.keyboard-visible .bedtime-edit-modal-actions {
    padding: 6px 12px 8px 12px !important;
    min-height: 40px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-btn-secondary,
  .modal-overlay.keyboard-visible .bedtime-edit-btn-primary {
    height: 32px !important;
    font-size: 12px !important;
    gap: 4px !important;
  }

  /* 虛擬鍵盤顯示時的工具列進一步壓縮 */
  .modal-overlay.keyboard-visible .bedtime-edit-stat-item {
    padding: 4px 8px !important;
    font-size: 10px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-stat-item strong {
    font-size: 14px !important;
    margin-bottom: 1px !important;
  }

  .modal-overlay.keyboard-visible .bedtime-edit-reset-default-btn,
  .modal-overlay.keyboard-visible .bedtime-edit-add-category-btn {
    height: 32px !important;
    font-size: 12px !important;
    gap: 4px !important;
  }

  /* 一般的刪除按鈕樣式（用於其他地方） */
  .bedtime-edit-btn-remove {
    width: 36px !important;
    height: 36px !important;
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 10px !important;
    font-size: 16px !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3) !important;
    position: relative !important;
    z-index: 1 !important;
  }

  .bedtime-edit-btn-remove:active {
    transform: scale(0.9) !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4) !important;
  }

  /* 空狀態 - 重新設計 */
  .bedtime-edit-empty-subitems {
    background: linear-gradient(135deg, #f3e5f5 0%, #fce4ec 100%) !important;
    border: 3px dashed #ba68c8 !important;
    border-radius: 16px !important;
    padding: 32px 20px !important;
    text-align: center !important;
    color: #7b1fa2 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
  }

  .bedtime-edit-add-first-subitem {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3) !important;
    margin-top: 16px !important;
  }

  .bedtime-edit-add-first-subitem:active {
    transform: scale(0.95) !important;
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.4) !important;
  }

  /* 空狀態佔位符 - 全新設計 */
  .bedtime-edit-empty-placeholder {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%) !important;
    border: 4px dashed #4caf50 !important;
    border-radius: 24px !important;
    padding: 48px 24px !important;
    text-align: center !important;
    margin: 20px 0 !important;
  }

  .bedtime-edit-empty-icon {
    font-size: 64px !important;
    margin-bottom: 20px !important;
    display: block !important;
  }

  .bedtime-edit-empty-placeholder h3 {
    font-size: 22px !important;
    font-weight: 700 !important;
    color: #2e7d32 !important;
    margin: 0 0 12px 0 !important;
  }

  .bedtime-edit-empty-placeholder p {
    font-size: 16px !important;
    color: #4caf50 !important;
    margin: 0 0 24px 0 !important;
    line-height: 1.5 !important;
  }

  .bedtime-edit-empty-action-btn {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 16px !important;
    padding: 16px 32px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 12px !important;
    margin: 0 auto !important;
  }

  .bedtime-edit-empty-action-btn:active {
    transform: scale(0.95) !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4) !important;
  }

  /* 底部操作區 - 極簡設計，最大化節省空間 */
  .bedtime-edit-modal-actions {
    flex-shrink: 0 !important;
    background: white !important;
    padding: 8px 16px 12px 16px !important;
    border-top: 1px solid #e8f5e8 !important;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    gap: 12px !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 48px !important;
  }

  /* 隱藏說明文字和info圖標以節省空間 */
  .bedtime-edit-help-text {
    display: none !important;
  }

  .bedtime-edit-actions-left {
    display: none !important;
  }

  /* 操作按鈕 - 左右排列 */
  .bedtime-edit-actions-right {
    display: flex !important;
    flex-direction: row !important;
    gap: 16px !important;
    width: 100% !important;
  }

  .bedtime-edit-btn-secondary,
  .bedtime-edit-btn-primary {
    flex: 1 !important;
    height: 40px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 700 !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    min-width: 100px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .bedtime-edit-btn-secondary {
    background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%) !important;
    color: white !important;
  }

  .bedtime-edit-btn-primary {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
    color: white !important;
  }

  .bedtime-edit-btn-secondary:active,
  .bedtime-edit-btn-primary:active {
    transform: scale(0.96) !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2) !important;
  }

  .bedtime-edit-btn-primary:disabled,
  .bedtime-edit-btn-secondary:disabled {
    opacity: 0.6 !important;
    transform: none !important;
    cursor: not-allowed !important;
  }
}
