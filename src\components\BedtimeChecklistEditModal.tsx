import React, { useState, useEffect } from 'react';
import { FaPlus, FaTrash, FaEdit, FaSave, FaTimes, FaInfoCircle, FaUndo } from 'react-icons/fa';
import BaseModal from './BaseModal';
import IconWrapper from './IconWrapper';
import { Database } from '../services/Database';
import { useAuth } from '../contexts/AuthContext';
import { useChild } from '../contexts/ChildContext';
import { ChecklistItem, ChecklistSubItem } from '../data/bedtimeChecklistData';
import { removeLocalStorageItem } from '../utils/localStorageUtils';
import { initVirtualKeyboardHandler, destroyVirtualKeyboardHandler } from '../utils/virtualKeyboardHandler';
import '../styles/BedtimeChecklistEditModal.css';

interface BedtimeChecklistEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const BedtimeChecklistEditModal: React.FC<BedtimeChecklistEditModalProps> = ({
  isOpen,
  onClose,
  onSave
}) => {
  const { currentUser } = useAuth();
  const { selectedChild } = useChild();
  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 獲取預設檢查表項目 - 與 bedtimeChecklistData.ts 完全一致
  const getDefaultItems = (): ChecklistItem[] => {
    return [
      {
        id: 'organize-bag',
        title: '整理書包',
        icon: '🎒',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'check-homework', title: '檢查作業是否完成', completed: false },
          { id: 'pack-textbooks', title: '收拾明天需要的課本', completed: false },
          { id: 'pack-stationery', title: '整理文具用品', completed: false },
          { id: 'check-notices', title: '檢查聯絡簿和通知單', completed: false }
        ]
      },
      {
        id: 'tidy-toys',
        title: '收拾玩具',
        icon: '🧸',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'put-toys-back', title: '將玩具放回原位', completed: false },
          { id: 'organize-lego', title: '確認沒有亂丟東西在地上或桌上', completed: false },
          { id: 'clean-play-area', title: '保持自己座位附近整潔', completed: false }
        ]
      },
      {
        id: 'share-daily-thoughts',
        title: '分享今日心得',
        icon: '💭',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'share-happy-moment', title: '分享今天最開心的事', completed: false },
          { id: 'share-learning', title: '說說今天學到了什麼', completed: false },
          { id: 'share-gratitude', title: '感謝今天幫助過我的人', completed: false }
        ]
      },
      {
        id: 'self-reflection',
        title: '自我反省',
        icon: '🤔',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'maintain-politeness', title: '保持禮貌好習慣', completed: false },
          { id: 'no-fighting', title: '沒有跟同學吵架', completed: false },
          { id: 'no-punishment', title: '沒有被罵或處罰', completed: false },
          { id: 'no-tantrums', title: '沒有哭鬧或鬧脾氣', completed: false }
        ]
      },
      {
        id: 'bedtime-routine',
        title: '睡前準備',
        icon: '🌙',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'brush-teeth', title: '睡前刷牙', completed: false },
          { id: 'drink-water', title: '記得上廁所', completed: false },
          { id: 'say-goodnight', title: '向家人道晚安', completed: false },
          { id: 'set-alarm', title: '設定明天的鬧鐘', completed: false }
        ]
      }
    ];
  };

  // 載入自訂睡前檢查表項目
  const loadCustomChecklist = async () => {
    if (!currentUser || !selectedChild) return;

    setLoading(true);
    try {
      const customData = await Database.getDoc('customBedtimeChecklist', 'template', currentUser.uid, selectedChild.id);
      if (customData && customData.items) {
        setChecklistItems(customData.items);
      } else {
        // 如果沒有自訂項目，載入預設項目 - 與 bedtimeChecklistData.ts 完全一致
        const defaultItems: ChecklistItem[] = [
          {
            id: 'organize-bag',
            title: '整理書包',
            icon: '🎒',
            completed: false,
            expanded: false,
            subItems: [
              { id: 'check-homework', title: '檢查作業是否完成', completed: false },
              { id: 'pack-textbooks', title: '收拾明天需要的課本', completed: false },
              { id: 'pack-stationery', title: '整理文具用品', completed: false },
              { id: 'check-notices', title: '檢查聯絡簿和通知單', completed: false }
            ]
          },
          {
            id: 'tidy-toys',
            title: '收拾玩具',
            icon: '�',
            completed: false,
            expanded: false,
            subItems: [
              { id: 'put-toys-back', title: '將玩具放回原位', completed: false },
              { id: 'organize-lego', title: '確認沒有亂丟東西在地上或桌上', completed: false },
              { id: 'clean-play-area', title: '保持自己座位附近整潔', completed: false }
            ]
          },
          {
            id: 'share-daily-thoughts',
            title: '分享今日心得',
            icon: '💭',
            completed: false,
            expanded: false,
            subItems: [
              { id: 'share-happy-moment', title: '分享今天最開心的事', completed: false },
              { id: 'share-learning', title: '說說今天學到了什麼', completed: false },
              { id: 'share-gratitude', title: '感謝今天幫助過我的人', completed: false }
            ]
          },
          {
            id: 'self-reflection',
            title: '自我反省',
            icon: '🤔',
            completed: false,
            expanded: false,
            subItems: [
              { id: 'maintain-politeness', title: '保持禮貌好習慣', completed: false },
              { id: 'no-fighting', title: '沒有跟同學吵架', completed: false },
              { id: 'no-punishment', title: '沒有被罵或處罰', completed: false },
              { id: 'no-tantrums', title: '沒有哭鬧或鬧脾氣', completed: false }
            ]
          },
          {
            id: 'bedtime-routine',
            title: '睡前準備',
            icon: '🌙',
            completed: false,
            expanded: false,
            subItems: [
              { id: 'brush-teeth', title: '睡前刷牙', completed: false },
              { id: 'drink-water', title: '記得上廁所', completed: false },
              { id: 'say-goodnight', title: '向家人道晚安', completed: false },
              { id: 'set-alarm', title: '設定明天的鬧鐘', completed: false }
            ]
          }
        ];
        setChecklistItems(defaultItems);
      }
    } catch (error) {
      console.error('載入自訂睡前檢查表失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存自訂睡前檢查表
  const saveCustomChecklist = async () => {
    if (!currentUser || !selectedChild) return;

    setSaving(true);
    try {
      const dataToSave = {
        items: checklistItems,
        lastUpdated: new Date().toISOString(),
        childId: selectedChild.id
      };

      await Database.setDoc('customBedtimeChecklist', 'template', dataToSave, currentUser.uid, selectedChild.id);
      console.log('自訂睡前檢查表已保存');

      // 檢查今日的睡前檢查表狀態，如果尚未提交則清除 localStorage 暫存
      const today = new Date().toISOString().split('T')[0];
      try {
        // 先檢查資料庫中是否有今日已提交的檢查表
        const todayChecklistData = await Database.getDoc('bedtimeChecklist', 'checklist', currentUser.uid, selectedChild.id, today);

        if (todayChecklistData && todayChecklistData.checklistData) {
          // 如果資料庫中有數據，說明已經提交過，不清除暫存
          console.log('今日睡前檢查表已提交，保持原有數據不變');
        } else {
          // 如果資料庫中沒有數據，說明尚未提交，清除 localStorage 暫存以便刷新
          console.log('今日睡前檢查表尚未提交，清除 localStorage 暫存以便刷新...');

          const storageKey = `bedtimeChecklist_${today}_${selectedChild.id}`;
          try {
            removeLocalStorageItem(storageKey, currentUser.uid);
            console.log('已清除 localStorage 中的今日睡前檢查表暫存');
            console.log('下次進入睡前檢查表將使用更新後的模板');
          } catch (localStorageError) {
            console.error('清除 localStorage 暫存失敗:', localStorageError);
          }
        }
      } catch (checkError) {
        console.error('檢查今日睡前檢查表狀態失敗:', checkError);
        // 如果檢查失敗，為了安全起見，還是清除 localStorage 暫存
        console.log('檢查失敗，為安全起見清除 localStorage 暫存');
        const storageKey = `bedtimeChecklist_${today}_${selectedChild.id}`;
        try {
          removeLocalStorageItem(storageKey, currentUser.uid);
        } catch (localStorageError) {
          console.error('清除 localStorage 暫存失敗:', localStorageError);
        }
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('保存自訂睡前檢查表失敗:', error);
    } finally {
      setSaving(false);
    }
  };

  // 新增大項目
  const addMainItem = () => {
    const newItem: ChecklistItem = {
      id: `custom-${Date.now()}`,
      title: '新項目',
      icon: '📝',
      completed: false,
      expanded: false,
      subItems: [
        { id: `sub-${Date.now()}`, title: '新子項目', completed: false }
      ]
    };
    setChecklistItems([...checklistItems, newItem]);
  };

  // 恢復預設項目 - 與 bedtimeChecklistData.ts 中的預設項目完全一致
  const resetToDefault = () => {
    const defaultItems: ChecklistItem[] = [
      {
        id: 'organize-bag',
        title: '整理書包',
        icon: '🎒',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'check-homework', title: '檢查作業是否完成', completed: false },
          { id: 'pack-textbooks', title: '收拾明天需要的課本', completed: false },
          { id: 'pack-stationery', title: '整理文具用品', completed: false },
          { id: 'check-notices', title: '檢查聯絡簿和通知單', completed: false }
        ]
      },
      {
        id: 'tidy-toys',
        title: '收拾玩具',
        icon: '🧸',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'put-toys-back', title: '將玩具放回原位', completed: false },
          { id: 'organize-lego', title: '確認沒有亂丟東西在地上或桌上', completed: false },
          { id: 'clean-play-area', title: '保持自己座位附近整潔', completed: false }
        ]
      },
      {
        id: 'share-daily-thoughts',
        title: '分享今日心得',
        icon: '💭',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'share-happy-moment', title: '分享今天最開心的事', completed: false },
          { id: 'share-learning', title: '說說今天學到了什麼', completed: false },
          { id: 'share-gratitude', title: '感謝今天幫助過我的人', completed: false }
        ]
      },
      {
        id: 'self-reflection',
        title: '自我反省',
        icon: '🤔',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'maintain-politeness', title: '保持禮貌好習慣', completed: false },
          { id: 'no-fighting', title: '沒有跟同學吵架', completed: false },
          { id: 'no-punishment', title: '沒有被罵或處罰', completed: false },
          { id: 'no-tantrums', title: '沒有哭鬧或鬧脾氣', completed: false }
        ]
      },
      {
        id: 'bedtime-routine',
        title: '睡前準備',
        icon: '🌙',
        completed: false,
        expanded: false,
        subItems: [
          { id: 'brush-teeth', title: '睡前刷牙', completed: false },
          { id: 'drink-water', title: '記得上廁所', completed: false },
          { id: 'say-goodnight', title: '向家人道晚安', completed: false },
          { id: 'set-alarm', title: '設定明天的鬧鐘', completed: false }
        ]
      }
    ];
    setChecklistItems(defaultItems);
  };

  // 刪除大項目
  const deleteMainItem = (itemId: string) => {
    setChecklistItems(checklistItems.filter(item => item.id !== itemId));
  };

  // 更新大項目
  const updateMainItem = (itemId: string, field: keyof ChecklistItem, value: any) => {
    setChecklistItems(checklistItems.map(item => 
      item.id === itemId ? { ...item, [field]: value } : item
    ));
  };

  // 新增子項目
  const addSubItem = (mainItemId: string) => {
    const newSubItem: ChecklistSubItem = {
      id: `sub-${Date.now()}`,
      title: '新子項目',
      completed: false
    };
    
    setChecklistItems(checklistItems.map(item => 
      item.id === mainItemId 
        ? { ...item, subItems: [...item.subItems, newSubItem] }
        : item
    ));
  };

  // 刪除子項目
  const deleteSubItem = (mainItemId: string, subItemId: string) => {
    setChecklistItems(checklistItems.map(item => 
      item.id === mainItemId 
        ? { ...item, subItems: item.subItems.filter(sub => sub.id !== subItemId) }
        : item
    ));
  };

  // 更新子項目
  const updateSubItem = (mainItemId: string, subItemId: string, field: keyof ChecklistSubItem, value: any) => {
    setChecklistItems(checklistItems.map(item => 
      item.id === mainItemId 
        ? { 
            ...item, 
            subItems: item.subItems.map(sub => 
              sub.id === subItemId ? { ...sub, [field]: value } : sub
            )
          }
        : item
    ));
  };

  useEffect(() => {
    if (isOpen) {
      loadCustomChecklist();
    }
  }, [isOpen, currentUser, selectedChild]);

  // 初始化虛擬鍵盤處理器
  useEffect(() => {
    if (isOpen) {
      // 只在移動端初始化虛擬鍵盤處理器
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isMobile) {
        const keyboardHandler = initVirtualKeyboardHandler({
          modalSelector: '.modal-overlay',
          debug: false,
          onKeyboardShow: () => {
            // 虛擬鍵盤顯示時的處理
            console.log('睡前檢查表編輯：虛擬鍵盤顯示');
          },
          onKeyboardHide: () => {
            // 虛擬鍵盤隱藏時的處理
            console.log('睡前檢查表編輯：虛擬鍵盤隱藏');
          }
        });

        // 清理函數
        return () => {
          destroyVirtualKeyboardHandler();
        };
      }
    }
  }, [isOpen]);

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="自訂睡前檢查表"
      description="為孩子建立專屬的睡前檢查項目，培養良好的生活習慣"
      maxWidth="800px"
      className="bedtime-checklist-edit-modal"
    >
      {loading ? (
        <div className="bedtime-edit-loading-container">
          <div className="bedtime-edit-loading-spinner">
            <div className="bedtime-edit-spinner"></div>
          </div>
          <p>載入檢查表設定中...</p>
        </div>
      ) : (
        <>
          <div className="bedtime-edit-modal-content">
            {/* 頂部工具列 */}
            <div className="bedtime-edit-toolbar">
              <div className="bedtime-edit-toolbar-left">
                <div className="stats">
                  <span className="bedtime-edit-stat-item">
                    <strong>{checklistItems.length}</strong> 個大項目
                  </span>
                  <span className="bedtime-edit-stat-item">
                    <strong>{checklistItems.reduce((total, item) => total + item.subItems.length, 0)}</strong> 個子項目
                  </span>
                </div>
              </div>
              <div className="bedtime-edit-toolbar-right">
                <button className="bedtime-edit-reset-default-btn" onClick={resetToDefault}>
                  <IconWrapper icon={FaUndo} />
                  恢復預設
                </button>
                <button className="bedtime-edit-add-category-btn" onClick={addMainItem}>
                  <IconWrapper icon={FaPlus} />
                  新增類別
                </button>
              </div>
            </div>

            {/* 編輯區域 */}
            <div className="bedtime-edit-area">
              {checklistItems.length === 0 ? (
                <div className="bedtime-edit-empty-placeholder">
                  <div className="bedtime-edit-empty-icon">🌙</div>
                  <h3>開始建立睡前檢查表</h3>
                  <p>點擊「新增類別」來建立第一個檢查項目類別</p>
                  <button className="bedtime-edit-empty-action-btn" onClick={addMainItem}>
                    <IconWrapper icon={FaPlus} />
                    新增第一個類別
                  </button>
                </div>
              ) : (
                <div className="bedtime-edit-categories-list">
                  {checklistItems.map((item, index) => (
                    <div key={item.id} className="bedtime-edit-category-card">
                      {/* 類別標題區 */}
                      <div className="bedtime-edit-category-header">
                        <div className="bedtime-edit-category-badge">{index + 1}</div>
                        <div className="bedtime-edit-category-inputs">
                          <input
                            type="text"
                            value={item.icon}
                            onChange={(e) => updateMainItem(item.id, 'icon', e.target.value)}
                            className="bedtime-edit-emoji-input"
                            placeholder="🔥"
                            maxLength={2}
                            title="選擇一個表情符號"
                          />
                          <input
                            type="text"
                            value={item.title}
                            onChange={(e) => updateMainItem(item.id, 'title', e.target.value)}
                            className="bedtime-edit-category-title-input"
                            placeholder="例如：個人衛生"
                          />
                        </div>
                        <div className="bedtime-edit-category-actions">
                          <button
                            className="bedtime-edit-btn-icon bedtime-edit-btn-add"
                            onClick={() => addSubItem(item.id)}
                            title="新增子項目"
                          >
                            <IconWrapper icon={FaPlus} />
                          </button>
                          <button
                            className="bedtime-edit-btn-icon bedtime-edit-btn-delete"
                            onClick={() => deleteMainItem(item.id)}
                            title="刪除類別"
                          >
                            <IconWrapper icon={FaTrash} />
                          </button>
                        </div>
                      </div>

                      {/* 子項目列表 */}
                      <div className="bedtime-edit-subitems-container">
                        <div className="bedtime-edit-subitems-header">
                          <span>檢查項目</span>
                          <span className="bedtime-edit-count">({item.subItems.length})</span>
                        </div>

                        <div className="bedtime-edit-subitems-list">
                          {item.subItems.map((subItem, subIndex) => (
                            <div key={subItem.id} className="bedtime-edit-subitem-row">
                              <button
                                className="bedtime-edit-btn-icon bedtime-edit-btn-remove"
                                onClick={() => deleteSubItem(item.id, subItem.id)}
                                title="移除項目"
                              >
                                <IconWrapper icon={FaTimes} />
                              </button>
                              <textarea
                                value={subItem.title}
                                onChange={(e) => {
                                  updateSubItem(item.id, subItem.id, 'title', e.target.value);
                                  // 自動調整高度
                                  e.target.style.height = 'auto';
                                  e.target.style.height = e.target.scrollHeight + 'px';
                                }}
                                className="bedtime-edit-subitem-input"
                                placeholder="例如：刷牙洗臉"
                                rows={1}
                                onInput={(e) => {
                                  // 確保在輸入時也調整高度
                                  const target = e.target as HTMLTextAreaElement;
                                  target.style.height = 'auto';
                                  target.style.height = target.scrollHeight + 'px';
                                }}
                              />
                            </div>
                          ))}

                          {item.subItems.length === 0 && (
                            <div className="bedtime-edit-empty-subitems">
                              <span>尚未新增檢查項目</span>
                              <button
                                className="bedtime-edit-add-first-subitem"
                                onClick={() => addSubItem(item.id)}
                              >
                                新增第一個項目
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 底部操作區 - 移到外層，參考TaskModal */}
          <div className="bedtime-edit-modal-actions">
            <div className="bedtime-edit-actions-left">
              <div className="bedtime-edit-help-text">
                <IconWrapper icon={FaInfoCircle} />
                設定完成後，新的睡前檢查表將使用這些項目
              </div>
            </div>
            <div className="bedtime-edit-actions-right">
              <button
                className="bedtime-edit-btn-secondary"
                onClick={onClose}
                disabled={saving}
              >
                取消
              </button>
              <button
                className="bedtime-edit-btn-primary"
                onClick={saveCustomChecklist}
                disabled={saving}
              >
                <IconWrapper icon={FaSave} />
                {saving ? '儲存中...' : '儲存設定'}
              </button>
            </div>
          </div>
        </>
      )}
    </BaseModal>
  );
};

export default BedtimeChecklistEditModal;
